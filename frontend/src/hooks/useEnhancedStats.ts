import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { dashboardApi, futureTransactionsApi, cacheApi } from '@/lib/api'
import { useFamilyMemberStats } from '@/hooks/useFamilyMembers'
import type { StatsFilters } from '@/components/stats/StatsFilters'

interface EnhancedStatsData {
  overview: any
  familyStats: any
  futureStats: any
  cacheStats: any
  performanceStats: any
  trendsData: any[]
  categoryData: any[]
  comparisonData?: any
}

export function useEnhancedStats(filters: StatsFilters) {
  // Fetch dashboard overview
  const { data: overview, isLoading: isLoadingOverview } = useQuery({
    queryKey: ['dashboard', 'overview', filters],
    queryFn: () => dashboardApi.getOverview({
      startDate: filters.startDate?.toISOString(),
      endDate: filters.endDate?.toISOString()
    }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  // Fetch family members stats
  const { data: familyStats, isLoading: isLoadingFamily } = useFamilyMemberStats()

  // Fetch future transactions stats
  const { data: futureStats, isLoading: isLoadingFuture } = useQuery({
    queryKey: ['future-transactions', 'stats', filters],
    queryFn: () => futureTransactionsApi.getStats(),
    staleTime: 2 * 60 * 1000,
  })

  // Fetch cache stats
  const { data: cacheStats, isLoading: isLoadingCache } = useQuery({
    queryKey: ['cache', 'stats'],
    queryFn: cacheApi.getStats,
    staleTime: 30 * 1000, // 30 seconds
  })

  // Fetch performance metrics
  const { data: performanceStats, isLoading: isLoadingPerformance } = useQuery({
    queryKey: ['dashboard', 'performance-metrics'],
    queryFn: dashboardApi.getPerformanceMetrics,
    staleTime: 1 * 60 * 1000, // 1 minute
  })

  // Mock trends data for now
  const trendsData = { data: { data: [] } }
  const isLoadingTrends = false

  // Mock category data for now
  const categoryData = { data: { data: [] } }
  const isLoadingCategories = false

  // Fetch comparison data if needed
  const { data: comparisonData, isLoading: isLoadingComparison } = useQuery({
    queryKey: ['dashboard', 'comparison', filters],
    queryFn: () => {
      if (!filters.compareWithPrevious || !filters.startDate || !filters.endDate) {
        return null
      }

      const periodDiff = filters.endDate.getTime() - filters.startDate.getTime()
      const previousStartDate = new Date(filters.startDate.getTime() - periodDiff)
      const previousEndDate = new Date(filters.endDate.getTime() - periodDiff)

      return dashboardApi.getOverview({
        startDate: previousStartDate.toISOString(),
        endDate: previousEndDate.toISOString()
      })
    },
    enabled: filters.compareWithPrevious,
    staleTime: 5 * 60 * 1000,
  })

  const isLoading = 
    isLoadingOverview || 
    isLoadingFamily || 
    isLoadingFuture || 
    isLoadingCache || 
    isLoadingPerformance ||
    isLoadingTrends ||
    isLoadingCategories ||
    (filters.compareWithPrevious && isLoadingComparison)

  const enhancedData = useMemo((): EnhancedStatsData => {
    return {
      overview: overview?.data || {},
      familyStats: familyStats || {},
      futureStats: futureStats?.data || {},
      cacheStats: cacheStats?.data?.data || {},
      performanceStats: performanceStats?.data?.data || {},
      trendsData: trendsData?.data?.data || [],
      categoryData: categoryData?.data?.data || [],
      comparisonData: comparisonData?.data || null
    }
  }, [
    overview,
    familyStats,
    futureStats,
    cacheStats,
    performanceStats,
    trendsData,
    categoryData,
    comparisonData
  ])

  // Generate enhanced stats cards data
  const statsCardsData = useMemo(() => {
    const data = enhancedData.overview
    const comparison = enhancedData.comparisonData
    
    return [
      {
        title: 'Saldo Total',
        value: data.totalBalance || 0,
        previousValue: comparison?.totalBalance,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-green-500 to-green-600',
        textColor: 'text-green-600',
        format: 'currency' as const,
        tooltip: 'Soma de todos os saldos das contas ativas'
      },
      {
        title: 'Receitas do Período',
        value: data.periodIncome || 0,
        previousValue: comparison?.periodIncome,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-blue-500 to-blue-600',
        textColor: 'text-blue-600',
        format: 'currency' as const,
        tooltip: 'Total de receitas no período selecionado'
      },
      {
        title: 'Despesas do Período',
        value: data.periodExpenses || 0,
        previousValue: comparison?.periodExpenses,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-red-500 to-red-600',
        textColor: 'text-red-600',
        format: 'currency' as const,
        tooltip: 'Total de despesas no período selecionado'
      },
      {
        title: 'Resultado do Período',
        value: (data.periodIncome || 0) - (data.periodExpenses || 0),
        previousValue: comparison ? (comparison.periodIncome || 0) - (comparison.periodExpenses || 0) : undefined,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-purple-500 to-purple-600',
        textColor: 'text-purple-600',
        format: 'currency' as const,
        tooltip: 'Diferença entre receitas e despesas do período'
      }
    ]
  }, [enhancedData.overview, enhancedData.comparisonData])

  return {
    data: enhancedData,
    statsCardsData,
    isLoading,
    error: null // TODO: Implement error handling
  }
}
