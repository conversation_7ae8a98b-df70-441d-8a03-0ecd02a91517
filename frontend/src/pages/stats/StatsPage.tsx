import { useState } from 'react'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  Database,
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  Server,
  Zap,
  Target,
  PieChart,
  LineChart
} from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency } from '@/lib/utils'
import { StatsFilters, type StatsFilters as StatsFiltersType } from '@/components/stats/StatsFilters'
import { EnhancedStatsCards } from '@/components/stats/EnhancedStatsCards'
import { FinancialTrendsChart } from '@/components/stats/FinancialTrendsChart'
import { CategoryDistributionChart } from '@/components/stats/CategoryDistributionChart'
import { useEnhancedStats } from '@/hooks/useEnhancedStats'

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color: string
  textColor: string
  description?: string
}

function StatCard({ title, value, icon: Icon, color, textColor, description }: StatCardProps) {
  return (
    <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${color} shadow-soft`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-semibold text-muted-foreground">{title}</p>
          <p className={`text-3xl font-bold ${textColor}`}>
            {typeof value === 'number' && (title.includes('Saldo') || title.includes('Receitas') || title.includes('Despesas'))
              ? formatCurrency(value)
              : value}
          </p>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      </div>
    </div>
  )
}

interface SectionHeaderProps {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

function SectionHeader({ title, description, icon: Icon }: SectionHeaderProps) {
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-deep shadow-glow">
        <Icon className="h-6 w-6 text-white" />
      </div>
      <div>
        <h2 className="text-xl font-bold text-gradient">{title}</h2>
        <p className="text-base text-muted-foreground">{description}</p>
      </div>
    </div>
  )
}

export function StatsPage() {
  // Initialize filters with current month
  const now = new Date()
  const [filters, setFilters] = useState<StatsFiltersType>({
    period: 'month',
    startDate: new Date(now.getFullYear(), now.getMonth(), 1),
    endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0),
    compareWithPrevious: false
  })

  // Fetch enhanced stats data
  const { data: enhancedData, statsCardsData: rawStatsCardsData, isLoading } = useEnhancedStats(filters)

  // Add icons to stats cards data
  const statsCardsData = rawStatsCardsData.map((card, index) => {
    const icons = [DollarSign, TrendingUp, TrendingDown, Target]
    return {
      ...card,
      icon: icons[index] || DollarSign
    }
  })

  // Prepare chart data
  const trendsChartData = enhancedData.trendsData.map((item: any) => ({
    date: item.date,
    income: item.income || 0,
    expenses: item.expenses || 0,
    balance: item.balance || 0,
    previousIncome: filters.compareWithPrevious ? item.previousIncome : undefined,
    previousExpenses: filters.compareWithPrevious ? item.previousExpenses : undefined,
    previousBalance: filters.compareWithPrevious ? item.previousBalance : undefined
  }))

  const categoryChartData = enhancedData.categoryData.map((item: any, index: number) => ({
    name: item.name,
    value: item.value,
    color: item.color,
    percentage: item.percentage,
    count: item.count
  }))

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <BarChart3 className="h-8 w-8" />
              Estatísticas Avançadas
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Análise completa das suas finanças com gráficos e comparações temporais
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <StatsFilters
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* Enhanced Stats Cards */}
      <div>
        <SectionHeader
          title="Visão Geral Financeira"
          description="Principais métricas financeiras com comparações temporais"
          icon={DollarSign}
        />
        <EnhancedStatsCards
          stats={statsCardsData}
          isLoading={isLoading}
          showComparison={filters.compareWithPrevious}
        />
      </div>

      {/* Financial Trends Chart */}
      <div>
        <SectionHeader
          title="Tendências Financeiras"
          description="Evolução das receitas, despesas e saldo ao longo do tempo"
          icon={LineChart}
        />
        <FinancialTrendsChart
          data={trendsChartData}
          isLoading={isLoading}
          showComparison={filters.compareWithPrevious}
        />
      </div>

      {/* Category Distribution */}
      <div>
        <SectionHeader
          title="Distribuição por Categorias"
          description="Análise detalhada dos gastos por categoria"
          icon={PieChart}
        />
        <CategoryDistributionChart
          data={categoryChartData}
          title="Despesas por Categoria"
          description="Distribuição percentual das despesas por categoria no período"
          isLoading={isLoading}
        />
      </div>

      {/* Additional Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Family Members Stats */}
        <div>
          <SectionHeader
            title="Membros da Família"
            description="Estatísticas dos membros cadastrados"
            icon={Users}
          />
          <div className="grid grid-cols-2 gap-4">
            <StatCard
              title="Total de Membros"
              value={(enhancedData.familyStats as any)?.totalMembers || 0}
              icon={Users}
              color="bg-gradient-deep"
              textColor="text-primary"
            />
            <StatCard
              title="Membros Ativos"
              value={(enhancedData.familyStats as any)?.activeMembers || 0}
              icon={CheckCircle}
              color="bg-success"
              textColor="text-success"
            />
          </div>
        </div>

        {/* Future Transactions Stats */}
        <div>
          <SectionHeader
            title="Transações Futuras"
            description="Estatísticas de transações agendadas"
            icon={Calendar}
          />
          <div className="grid grid-cols-2 gap-4">
            <StatCard
              title="Vence Hoje"
              value={enhancedData.futureStats.dueToday || 0}
              icon={Clock}
              color="bg-warning"
              textColor="text-warning"
            />
            <StatCard
              title="Em Atraso"
              value={enhancedData.futureStats.overdue || 0}
              icon={AlertTriangle}
              color="bg-destructive"
              textColor="text-destructive"
            />
          </div>
        </div>
      </div>

      {/* System Performance Stats */}
      <div>
        <SectionHeader
          title="Performance do Sistema"
          description="Métricas de performance e cache"
          icon={Server}
        />
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Cache Ativo"
            value={enhancedData.cacheStats.isAvailable ? 'Sim' : 'Não'}
            icon={Database}
            color={enhancedData.cacheStats.isAvailable ? 'bg-success' : 'bg-destructive'}
            textColor={enhancedData.cacheStats.isAvailable ? 'text-success' : 'text-destructive'}
          />
          <StatCard
            title="Chaves em Cache"
            value={enhancedData.cacheStats.keyCount || 0}
            icon={Zap}
            color="bg-gradient-deep"
            textColor="text-primary"
          />
          <StatCard
            title="Queries Lentas"
            value={enhancedData.performanceStats.slowQueries?.length || 0}
            icon={AlertTriangle}
            color="bg-warning"
            textColor="text-warning"
            description="Queries > 2s"
          />
          <StatCard
            title="Total de Transações"
            value={enhancedData.overview.transactionsCount || 0}
            icon={Activity}
            color="bg-info"
            textColor="text-info"
          />
        </div>
      </div>

    </div>
  )
}
