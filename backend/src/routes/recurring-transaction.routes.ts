import { Router } from 'express'
import { z } from 'zod'
import prisma from '../lib/prisma'
import { authenticateToken } from '../middleware/auth.middleware'
import { validateRequest } from '../middleware/validation.middleware'
import { RecurrenceFrequency, TransactionType } from '@prisma/client'

const router = Router()

// Schema for creating recurring transactions
const createRecurringTransactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  fixedAmount: z.number().positive('Valor deve ser positivo'),
  frequency: z.nativeEnum(RecurrenceFrequency),
  startDate: z.string().datetime('Data de início inválida'),
  endDate: z.string().datetime('Data final inválida').optional(),
  type: z.nativeEnum(TransactionType),
  accountId: z.string().cuid('ID da conta inválido'),
  categoryId: z.string().cuid('ID da categoria inválido').optional(),
  tagIds: z.array(z.string().cuid()).optional(),
  familyMemberId: z.string().cuid('ID do membro da família inválido').optional()
})

// Helper function to calculate next occurrence date
function getNextOccurrenceDate(date: Date, frequency: RecurrenceFrequency): Date {
  const nextDate = new Date(date)
  
  switch (frequency) {
    case 'DAILY':
      nextDate.setDate(nextDate.getDate() + 1)
      break
    case 'WEEKLY':
      nextDate.setDate(nextDate.getDate() + 7)
      break
    case 'BIWEEKLY':
      nextDate.setDate(nextDate.getDate() + 14)
      break
    case 'MONTHLY':
      nextDate.setMonth(nextDate.getMonth() + 1)
      break
    case 'BIANNUAL':
      nextDate.setMonth(nextDate.getMonth() + 6)
      break
    case 'YEARLY':
      nextDate.setFullYear(nextDate.getFullYear() + 1)
      break
  }
  
  return nextDate
}

// Helper function to generate recurring transactions
async function generateRecurringTransactions(recurringTransactionId: string, userId: string) {
  console.log('generateRecurringTransactions called with:', { recurringTransactionId, userId })

  const recurringTransaction = await prisma.recurringTransaction.findUnique({
    where: { id: recurringTransactionId }
  })

  console.log('Found recurring transaction:', recurringTransaction)

  if (!recurringTransaction || !recurringTransaction.isActive) {
    console.log('Recurring transaction not found or not active')
    return []
  }

  const transactions = []
  let currentDate = new Date(recurringTransaction.startDate)
  const endDate = recurringTransaction.endDate ? new Date(recurringTransaction.endDate) : null
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  while (!endDate || currentDate <= endDate) {
    // Stop if we've generated too many future transactions (safety limit)
    if (currentDate > new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)) {
      break
    }

    const isPastTransaction = currentDate < today
    
    const transaction = await prisma.transaction.create({
      data: {
        description: recurringTransaction.description,
        totalAmount: recurringTransaction.fixedAmount!,
        totalInstallments: 1,
        transactionDate: currentDate,
        type: recurringTransaction.type,
        accountId: recurringTransaction.accountId,
        categoryId: recurringTransaction.categoryId,
        isFuture: !isPastTransaction,
        userId,
        installments: {
          create: {
            installmentNumber: 1,
            amount: recurringTransaction.fixedAmount!,
            dueDate: currentDate,
            isPaid: isPastTransaction, // Past transactions are marked as paid
            description: recurringTransaction.description,
            userId
          }
        }
      },
      include: {
        installments: true,
        account: true,
        category: true
      }
    })

    // Add tags if they exist
    if (recurringTransaction.categoryId) {
      // Get tags from recurring transaction (we'll need to store them separately)
      // For now, we'll skip tags in recurring transactions
    }

    // Add family member if exists
    if (recurringTransaction.categoryId) {
      // Similar to tags, we'll need to handle family members
      // For now, we'll skip family members in recurring transactions
    }

    transactions.push(transaction)
    currentDate = getNextOccurrenceDate(currentDate, recurringTransaction.frequency)
  }

  return transactions
}

// POST /api/v1/recurring-transactions - Create recurring transaction
router.post('/', 
  authenticateToken,
  validateRequest(createRecurringTransactionSchema),
  async (req, res) => {
    try {
      const userId = req.user!.id
      const data = req.body

      console.log('Creating recurring transaction with data:', data)
      console.log('User ID:', userId)

      // Create the recurring transaction record
      let recurringTransaction
      try {
        console.log('About to create recurring transaction with data:', {
          description: data.description,
          fixedAmount: data.fixedAmount,
          frequency: data.frequency,
          startDate: new Date(data.startDate),
          endDate: data.endDate ? new Date(data.endDate) : null,
          type: data.type,
          accountId: data.accountId,
          categoryId: data.categoryId,
          isActive: true,
          userId
        })

        recurringTransaction = await prisma.recurringTransaction.create({
          data: {
            description: data.description,
            fixedAmount: data.fixedAmount,
            frequency: data.frequency,
            startDate: new Date(data.startDate),
            endDate: data.endDate ? new Date(data.endDate) : null,
            type: data.type,
            accountId: data.accountId,
            categoryId: data.categoryId,
            isActive: true,
            userId
          }
        })
        console.log('Recurring transaction created successfully:', recurringTransaction)
        console.log('Recurring transaction ID:', recurringTransaction?.id)
      } catch (createError) {
        console.error('Error creating recurring transaction in database:', createError)
        console.error('Error details:', JSON.stringify(createError, null, 2))
        throw createError
      }

      console.log('Created recurring transaction:', recurringTransaction)

      // Check if recurring transaction was created successfully
      if (!recurringTransaction || !recurringTransaction.id) {
        throw new Error('Failed to create recurring transaction - no ID returned')
      }

      // Generate the actual transactions
      const generatedTransactions = await generateRecurringTransactions(
        recurringTransaction.id,
        userId
      )

      console.log('Generated transactions count:', generatedTransactions.length)

      // Serialize the recurring transaction for JSON response
      const serializedRecurringTransaction = {
        ...recurringTransaction,
        fixedAmount: recurringTransaction.fixedAmount ? Number(recurringTransaction.fixedAmount) : null,
        percentageAmount: recurringTransaction.percentageAmount ? Number(recurringTransaction.percentageAmount) : null
      }

      res.status(201).json({
        success: true,
        data: {
          recurringTransaction: serializedRecurringTransaction,
          generatedTransactions: generatedTransactions.length,
          transactions: generatedTransactions.slice(0, 5) // Return first 5 for preview
        }
      })
    } catch (error) {
      console.error('Error creating recurring transaction:', error)
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }
)

// GET /api/v1/recurring-transactions - List recurring transactions
router.get('/',
  authenticateToken,
  async (req, res) => {
    try {
      const userId = req.user!.id

      const recurringTransactions = await prisma.recurringTransaction.findMany({
        where: { userId },
        include: {
          account: true,
          category: true
        },
        orderBy: { createdAt: 'desc' }
      })

      // Convert Decimal fields to numbers for JSON serialization
      const serializedTransactions = recurringTransactions.map(transaction => ({
        ...transaction,
        fixedAmount: transaction.fixedAmount ? Number(transaction.fixedAmount) : null,
        percentageAmount: transaction.percentageAmount ? Number(transaction.percentageAmount) : null
      }))

      res.json({
        success: true,
        data: serializedTransactions
      })
    } catch (error) {
      console.error('Error fetching recurring transactions:', error)
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }
)

// DELETE /api/v1/recurring-transactions/:id - Delete recurring transaction
router.delete('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const userId = req.user!.id
      const { id } = req.params

      // Deactivate the recurring transaction
      await prisma.recurringTransaction.update({
        where: { 
          id,
          userId 
        },
        data: { isActive: false }
      })

      res.json({
        success: true,
        message: 'Transação recorrente desativada com sucesso'
      })
    } catch (error) {
      console.error('Error deactivating recurring transaction:', error)
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }
)

// GET /api/v1/recurring-transactions/generate-pending - Generate pending transactions from recurring
router.get('/generate-pending',
  authenticateToken,
  async (req, res) => {
    try {
      const userId = req.user!.id
      const { months = 3 } = req.query // Generate for next 3 months by default

      const recurringTransactions = await prisma.recurringTransaction.findMany({
        where: {
          userId,
          isActive: true,
          deletedAt: null
        },
        include: {
          account: true,
          category: true
        }
      })

      const pendingTransactions = []
      const today = new Date()
      const endDate = new Date()
      endDate.setMonth(endDate.getMonth() + Number(months))

      for (const recurring of recurringTransactions) {
        const transactions = await generateRecurringTransactions(recurring.id, userId, endDate)
        pendingTransactions.push(...transactions)
      }

      res.json({
        success: true,
        data: {
          count: pendingTransactions.length,
          transactions: pendingTransactions
        }
      })
    } catch (error) {
      console.error('Error generating pending transactions:', error)
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }
)

export default router
